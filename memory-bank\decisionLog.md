# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-25 11:14:08 - Log of updates made.

*

## Decision

*   [2025-07-25 12:13:58] - **决策**: 放弃所有基于 `ACGAN_FG.py`（一个被错误修改过的版本）的复杂模型和实验。
*   [2025-07-25 12:35:42] - **决策**: **确立 `ACGAN_FG-yuanma.py` 为项目的唯一、正确的基线源码。** 该源码的架构与论文[1]完全一致。
*   [2025-07-25 12:35:42] - **诊断**: 既然原始源码架构正确，但复现结果依然不佳，问题根源必定在于**超参数设置、数据加载/处理方式，或训练流程**与论文的隐含条件不一致。

## Rationale

*   **性能不可接受**: 60% 的准确率对于发表高水平研究论文是远远不够的。
*   **根本性设计缺陷**: 多个创新点未经独立验证就进行集成，导致了无法控制的相互干扰。例如，自适应语义距离与循环一致性可能存在目标冲突。
*   **训练无法收敛**: 损失函数持续上升和梯度爆炸表明模型无法有效学习。
*   **高风险低回报**: 在当前复杂架构上继续投入时间进行调优，成功的可能性极低，风险远大于潜在回报。
*   **基线不可靠**: 连项目的基础代码都无法复现论文结果，表明可能存在代码、数据处理或环境配置上的未知陷阱，使得在其之上构建任何新模型都变得不可靠。

## Implementation Details

*   **立即行动**: 停止所有基于当前 ASDCGAN 架构的实验和优化。
*   **新方向**: **诊断并建立一个我们自己能够完全理解、控制和复现的基线模型。** 必须首先解决 `ACGAN-FG.py` 的复现问题，或者选择一个更简单、更公认的GAN模型（如标准的WGAN-GP）作为起点。
*   **迭代方法**: 在稳定的基线模型上，每次只引入并验证一个创新点，确保其有效性和稳定性，然后再考虑集成。
*   **目标**: 建立一个稳定、可复现的基线，其性能应显著优于随机猜测，然后再逐步进行改进。

## Rationale

*

## Implementation Details

*
---
**Timestamp:** 2025-07-25T15:03:35Z
**Decision:** 采纳用户提供的“直接使用容器IP访问无端口映射的Docker服务”的解决方案。
**Reason:** 该方法无需重建或停止现有容器，通过 `docker inspect` 获取IP，并在容器内使用 `--bind_all` 启动服务，操作简单、快速、高效。
**Outcome:** 成功解决了TensorBoard的访问问题。详细步骤已存为新模式文件 `pattern-docker-no-port-mapping-access.md`。